import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AuthService {
  static String get baseUrl =>
      dotenv.env['BASE_URL'] ?? 'http://127.0.0.1:8000/api';
  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  // Storage keys
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';

  /// Login with email and password
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      print('Making login request to: $baseUrl/login'); // Debug log

      final response = await http.post(
        Uri.parse('$baseUrl/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({'email': email, 'password': password}),
      );

      print('Response status: ${response.statusCode}'); // Debug log
      print('Response body: ${response.body}'); // Debug log

      // Check if response body is empty
      if (response.body.isEmpty) {
        return {'success': false, 'message': 'Server returned empty response'};
      }

      // Try to parse JSON response
      Map<String, dynamic> responseData;
      try {
        responseData = jsonDecode(response.body);
      } catch (jsonError) {
        return {
          'success': false,
          'message':
              'Invalid JSON response from server. Response: ${response.body.substring(0, response.body.length > 100 ? 100 : response.body.length)}...',
        };
      }

      if (response.statusCode == 200) {
        // Store token and user data securely
        await _storage.write(
          key: _tokenKey,
          value: responseData['access_token'],
        );
        await _storage.write(
          key: _userKey,
          value: jsonEncode(responseData['user']),
        );

        return {
          'success': true,
          'message': responseData['message'],
          'user': responseData['user'],
          'token': responseData['access_token'],
        };
      } else {
        // Handle validation errors
        String errorMessage = 'Login failed';
        if (responseData['errors'] != null) {
          final errors = responseData['errors'] as Map<String, dynamic>;
          errorMessage = errors.values.first[0] ?? errorMessage;
        } else if (responseData['message'] != null) {
          errorMessage = responseData['message'];
        }

        return {'success': false, 'message': errorMessage};
      }
    } catch (e) {
      print('Login error: $e'); // Debug log
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Logout user
  Future<Map<String, dynamic>> logout() async {
    try {
      final token = await getToken();

      if (token != null) {
        final response = await http.post(
          Uri.parse('$baseUrl/logout'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );

        // Clear local storage regardless of API response
        await clearAuthData();

        if (response.statusCode == 200) {
          final responseData = jsonDecode(response.body);
          return {
            'success': true,
            'message': responseData['message'] ?? 'Logged out successfully',
          };
        }
      }

      // Clear local data even if API call fails
      await clearAuthData();
      return {'success': true, 'message': 'Logged out successfully'};
    } catch (e) {
      // Clear local data even if there's an error
      await clearAuthData();
      return {'success': true, 'message': 'Logged out successfully'};
    }
  }

  /// Get stored authentication token
  Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }

  /// Get stored user data
  Future<Map<String, dynamic>?> getUser() async {
    final userJson = await _storage.read(key: _userKey);
    if (userJson != null) {
      return jsonDecode(userJson);
    }
    return null;
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// Clear all authentication data
  Future<void> clearAuthData() async {
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _userKey);
  }

  /// Make authenticated API request
  Future<http.Response> authenticatedRequest(
    String endpoint, {
    String method = 'GET',
    Map<String, dynamic>? body,
  }) async {
    final token = await getToken();

    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };

    final uri = Uri.parse('$baseUrl$endpoint');

    switch (method.toUpperCase()) {
      case 'POST':
        return await http.post(
          uri,
          headers: headers,
          body: body != null ? jsonEncode(body) : null,
        );
      case 'PUT':
        return await http.put(
          uri,
          headers: headers,
          body: body != null ? jsonEncode(body) : null,
        );
      case 'DELETE':
        return await http.delete(uri, headers: headers);
      default:
        return await http.get(uri, headers: headers);
    }
  }

  /// Validate token by making a test API call
  Future<bool> validateToken() async {
    try {
      final response = await authenticatedRequest('/user');
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
