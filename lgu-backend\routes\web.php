<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\IncidentController;

//THIS web.php is for the web browser version which uses blade templates
//Call the Controller Methods Directly and Convert the JSON response to an array


Route::get('/', function () {
    return view('welcome');
});

Route::get('/incidents', function () {
    $controller = new IncidentController();
    $request = request(); // pass current request if needed
    $response = $controller->index($request);
    $json = $response->getData(true); // turn JsonResponse into array

    return view('incidents', ['data' => $json['data']]);
});