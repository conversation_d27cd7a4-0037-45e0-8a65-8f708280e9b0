<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

use App\Models\User;
use Illuminate\Support\Facades\Hash;

// Create a test user
$user = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Test User',
        'password' => Hash::make('password123'),
        'email_verified_at' => now(),
    ]
);

echo "Test user created/found: " . $user->email . " (ID: " . $user->id . ")\n";
echo "Password: password123\n";
